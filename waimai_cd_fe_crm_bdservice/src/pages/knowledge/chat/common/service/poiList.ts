import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import { useState } from 'react';
import { useDebounceEffect, usePagination } from 'ahooks';

export type PoiItem = APISpec['/bee/v1/bdaiassistant/common/getOwnPoiListByPage']['response']['poiList'][number];
const getPoiListByPage = async (pageNum, pageSize = 10, keyword = '') => {
    const res = await apiCaller.get('/bee/v1/bdaiassistant/common/getOwnPoiListByPage', {
        pageSize: pageSize as any,
        pageNum,
        data: keyword,
    });
    if (res.code === 0) {
        return {
            list: res.data?.poiList || [],
            total: (res.data as any)?.total,
        };
    }
    return null;
};
const usePoiList = () => {
    const [searchValue, setSearchValue] = useState('');
    const [hasMore, setHasMore] = useState(true);

    const { data, pagination, loading } = usePagination(async ({ current, pageSize = 10 }) => {
        const resData = await getPoiListByPage(current, pageSize, searchValue);
        if (current >= resData?.total / pageSize) {
            setHasMore(false);
        }
        return resData
            ? {
                  list: [...(current === 1 ? [] : data?.list || []), ...resData.list],
                  total: resData.total,
              }
            : data;
    });
    const onScroll = (e: any) => {
        if (
            hasMore &&
            Math.abs(e.currentTarget.scrollHeight - e.currentTarget.scrollTop - e.currentTarget.clientHeight) <= 10
        ) {
            pagination.onChange(pagination.current + 1, 10);
        }
    };
    useDebounceEffect(() => {
        pagination.onChange(1, 10);
    }, [searchValue]);

    return {
        data: data as { list: PoiItem[]; total: number },
        pagination,
        searchValue,
        setSearchValue,
        onScroll,
        loading,
    };
};
export default usePoiList;
