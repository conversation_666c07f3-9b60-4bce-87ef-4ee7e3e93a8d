/**
 * HTML代码生成器入口文件
 *
 * 功能特性：
 * 1. 示意图文件上传到S3
 * 2. 可编辑的Prompt模板
 * 3. JSON数据输入和验证
 * 4. HTML代码生成和预览
 * 5. 通信代码自动集成
 * 6. 响应式设计支持
 * 7. 代码导出和复制功能
 */

import HtmlGenerator from './components/HtmlGenerator';

export default HtmlGenerator;

// 导出相关工具函数和类型，供其他模块使用
export { createCommunicator, injectCommunicator, getDefaultHtmlTemplate } from './utils/communicator';

export {
    getDefaultPromptTemplate,
    processPromptTemplate,
    validateJsonData,
    formatJsonString,
    getExampleJsonData,
} from './utils/promptTemplate';

export { generateHtmlCode, validateHtmlCode } from './utils/htmlGenerator';

export type { HtmlGeneratorConfig, HtmlGenerationResult } from './utils/htmlGenerator';
