export interface TextMessage {
    type: 'text';
    insert: string;
}
export interface StyledMessage {
    type: 'styledText';
    insert: string;
    attributes: {
        color: string;
        bold: boolean;
    };
}
export interface LinkMessage {
    type: 'link';
    insert: string;
    attributes: {
        link: string;
    };
}
export interface VideoMessage {
    type: 'video';
    insert: { video: string };
}
export interface ImageMessage {
    type: 'image';
    insert: { image: string };
}
export interface MediaMessage {
    type: 'media';
    insert: { media: (ImageMessage['insert'] | VideoMessage['insert'])[] };
}

export interface OptionsMessage {
    type: 'options';
    insert: {
        options: {
            abilityType: 1; // 枚举值，后端返回，透传回后端
            subAbilityType: 1; // 枚举值，后端返回，透传回后端
            operationType: 1; // 枚举值，必填，1：跳转链接，2：发送消息
            content: ''; // 展示内容，必填
            url: ''; // 可选，operationType为1时的跳链
        }[];
    };
}
export interface SuffixOptionsMessage {
    type: 'suffixOptions';
    insert: {
        suffixOptions: {
            options: {
                abilityType: number; // 枚举值，后端返回，透传回后端
                subAbilityType: number; // 枚举值，后端返回，透传回后端
                operationType: 1 | 2; // 枚举值，必填，1：跳转链接，2：发送消息
                content: string; // 展示内容，必填
                url: string;
            }[];
            descriptions: string;
        };
    };
}
export interface SeparatorMessage {
    type: 'separator';
    insert: {
        separator: 'nextMessage';
    };
}
export interface ButtonsMessage {
    type: 'buttons';
    insert: {
        buttons: {
            text: string;
            url?: string;
            action?: string;
            color?: string;
            type?: 'primary' | 'normal';
        }[];
    };
}

export interface MarkdownMessage {
    type: 'markdown';
    insert: {
        markdown: {
            text: string;
        };
    };
}

export interface UnknownMessage {
    type: 'unknown';
    insert: any;
}
export interface CardWithAvatarMessage {
    type: 'cardWithAvatar';
    insert: {
        cardWithAvatar: {
            type: string; // 透传给后端用于数据处理
            avatar: string; // 头像，不传则不展示
            title: string; // 超出一行则中间省略
            content: {
                // label和value之间增加冒号展示
                label: string;
                value: string;
                key: string;
                block?: boolean; // 表示是否占满一行，默认一行展示两个数据
            }[];
        };
    };
}

export interface SelectorItemMessage {
    // 两行文本，标题+内容
    type: 'selectorItem';
    insert: {
        selectorItem: {
            type: 'reject' | 'poi' | 'poi_private' | 'poi_public'; // 透传给后端，前端不使用，全部类型：reject
            title: string; // 标题，黑色加粗
            content: {
                label: string;
                value: string | number;
                show?: boolean;
                key?: 'label' | 'tag' | 'avatar' | 'online' | 'ID';
            }[]; // 内容，灰色字体
        };
    };
}

export interface SelectorMessage {
    type: 'selector';
    insert: {
        selector: {
            content: SelectorItemMessage['insert']['selectorItem'][];
            titleInIm: string; // 聊天页面的title，为空则不展示
            titleInSelector: string; // 驳回原因选择器上的title
            showDivider: boolean; // 控制是否展示分割线
            extendButtonName: string; // 展开按钮文本，为空或者reasons数组长度小于showNum则不展示按钮
            showNum: number; // 直接展示的数量，为null则不限制
        };
    };
}

export interface Config {
    type: 'config';
    insert: {
        config: {
            style: {
                backgroundColor: string;
            };
        };
    };
}

export type Message =
    | TextMessage
    | StyledMessage
    | LinkMessage
    | VideoMessage
    | ImageMessage
    | OptionsMessage
    | SeparatorMessage
    | MediaMessage
    | ButtonsMessage
    | SuffixOptionsMessage
    | MarkdownMessage
    | UnknownMessage
    | CardWithAvatarMessage
    | SelectorMessage
    | SelectorItemMessage
    | Config;

// export type ALL_KEYS = Message['type'];
export const ALL_KEYS = [
    'text',
    'styledText',
    'link',
    'video',
    'image',
    'media',
    'options',
    'suffixOptions',
    'separator',
    'buttons',
    'markdown',
    'unknown',
    'cardWithAvatar',
    'selectorItem',
    'selector',
    'config',
    'table',
];
