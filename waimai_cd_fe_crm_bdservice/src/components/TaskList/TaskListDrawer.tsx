import React, { useState, useEffect } from 'react';
import { Drawer, Tabs, Empty, Spin, Typography, Row, Col, Statistic } from 'antd';
import { useRequest } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import TaskListItem, { TaskItem } from './TaskListItem';
import dayjs from 'dayjs';

const { Title } = Typography;

interface TaskListDrawerProps {
    open: boolean;
    onClose: () => void;
    onSendMessage: (content: string) => void;
}

interface TaskGroup {
    createTime: string;
    itemList: TaskItem[];
}

interface TaskListResponse {
    jobList: TaskGroup[];
    total: number;
    success: number;
    fail: number;
}

const TaskListDrawer: React.FC<TaskListDrawerProps> = ({ open, onClose, onSendMessage }) => {
    const [activeTab, setActiveTab] = useState<'all' | 'running' | 'success' | 'fail'>('all');

    // 获取任务列表
    const {
        data: taskData,
        loading,
        run: refreshTasks,
    } = useRequest(
        async () => {
            const res = await apiCaller.send('/bee/v2/bdaiassistant/job/list', {});
            if (res.code === 0) {
                return res.data;
            }
            return null;
        },
        {
            manual: true,
            onError: error => {
                console.error('获取任务列表失败:', error);
            },
        },
    );

    // 当抽屉打开时获取任务列表
    useEffect(() => {
        if (open) {
            refreshTasks();
        }
    }, [open, refreshTasks]);

    // 处理任务列表数据
    const getAllTasks = (): TaskItem[] => {
        if (!taskData?.jobList) return [];

        const allTasks: TaskItem[] = [];
        taskData.jobList.forEach(group => {
            group.itemList.forEach(item => {
                allTasks.push({
                    ...item,
                    createTime: group.createTime,
                });
            });
        });

        // 按创建时间倒序排列
        return allTasks.sort((a, b) => dayjs(b.createTime).valueOf() - dayjs(a.createTime).valueOf());
    };

    const getFilteredTasks = (status?: string): TaskItem[] => {
        const allTasks = getAllTasks();
        if (!status) return allTasks;
        return allTasks.filter(task => task.status === status);
    };

    const handleViewResult = (item: TaskItem) => {
        // 构建查询消息
        const queryMessage = `请帮我查看商家${item.poiName}(ID: ${item.poiId})的诊断结果`;

        // 发送消息到聊天界面
        onSendMessage(queryMessage);

        // 关闭抽屉
        onClose();
    };

    const renderTaskList = (tasks: TaskItem[]) => {
        if (tasks.length === 0) {
            return <Empty description="暂无任务" style={{ marginTop: 60 }} />;
        }

        return (
            <div style={{ maxHeight: 'calc(100vh - 300px)', overflow: 'auto' }}>
                {tasks.map((item, index) => (
                    <TaskListItem key={`${item.poiId}-${index}`} item={item} onViewResult={handleViewResult} />
                ))}
            </div>
        );
    };

    const runningTasks = getFilteredTasks('init');
    const successTasks = getFilteredTasks('success');
    const failTasks = getFilteredTasks('fail');
    const allTasks = getAllTasks();

    const tabItems = [
        {
            key: 'all',
            label: `全部 (${allTasks.length})`,
            children: renderTaskList(allTasks),
        },
        {
            key: 'running',
            label: `进行中 (${runningTasks.length})`,
            children: renderTaskList(runningTasks),
        },
        {
            key: 'success',
            label: `成功 (${successTasks.length})`,
            children: renderTaskList(successTasks),
        },
        {
            key: 'fail',
            label: `失败 (${failTasks.length})`,
            children: renderTaskList(failTasks),
        },
    ];

    return (
        <Drawer
            title="今日任务"
            open={open}
            onClose={onClose}
            width={600}
            destroyOnClose
            styles={{
                body: {
                    padding: 0,
                },
            }}
        >
            <Spin spinning={loading}>
                <div style={{ padding: '20px 24px 0' }}>
                    <Title level={4} style={{ marginBottom: 16 }}>
                        任务统计
                    </Title>
                    <Row gutter={16} style={{ marginBottom: 24 }}>
                        <Col span={6}>
                            <Statistic title="总计" value={allTasks.length} valueStyle={{ color: '#1890ff' }} />
                        </Col>
                        <Col span={6}>
                            <Statistic title="进行中" value={runningTasks.length} valueStyle={{ color: '#1890ff' }} />
                        </Col>
                        <Col span={6}>
                            <Statistic title="成功" value={successTasks.length} valueStyle={{ color: '#52c41a' }} />
                        </Col>
                        <Col span={6}>
                            <Statistic title="失败" value={failTasks.length} valueStyle={{ color: '#ff4d4f' }} />
                        </Col>
                    </Row>
                </div>

                <Tabs
                    activeKey={activeTab}
                    onChange={key => setActiveTab(key as any)}
                    items={tabItems}
                    style={{ padding: '0 24px' }}
                />
            </Spin>
        </Drawer>
    );
};

export default TaskListDrawer;
