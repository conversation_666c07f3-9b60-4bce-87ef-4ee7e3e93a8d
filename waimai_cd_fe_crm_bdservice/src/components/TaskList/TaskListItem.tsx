import React from 'react';
import { Avatar, Button, Space, Typography, Tag } from 'antd';
import { ShopOutlined, EyeOutlined, LoadingOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

const { Text } = Typography;

export interface TaskItem {
    /** 任务类型 */
    type: string;
    /** 商家ID */
    poiId: number;
    /** 商家名称 */
    poiName: string;
    /** 商家头像 */
    poiAvator: string;
    /** 任务状态 success: 成功；fail : 失败；init: 运行中；*/
    status: string;
    /** 能力类型 */
    abilityType: number;
    /** 操作类型 */
    operationType: number;
    /** 内容 */
    content: string;
    /** 创建时间 */
    createTime: string;
}

interface TaskListItemProps {
    item: TaskItem;
    onViewResult: (item: TaskItem) => void;
}

const TaskListItem: React.FC<TaskListItemProps> = ({ item, onViewResult }) => {
    const getStatusTag = (status: string) => {
        switch (status) {
            case 'success':
                return <Tag color="success">成功</Tag>;
            case 'fail':
                return <Tag color="error">失败</Tag>;
            case 'init':
                return (
                    <Tag color="processing" icon={<LoadingOutlined />}>
                        进行中
                    </Tag>
                );
            default:
                return <Tag color="default">{status}</Tag>;
        }
    };

    const canViewResult = item.status === 'success' || item.status === 'fail';

    return (
        <div
            style={{
                display: 'flex',
                alignItems: 'center',
                padding: '16px 0',
                borderBottom: '1px solid #f0f0f0',
            }}
        >
            <Avatar src={item.poiAvator} icon={<ShopOutlined />} size={40} style={{ marginRight: 12 }} />

            <div style={{ flex: 1 }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                    <Text strong style={{ fontSize: 14, marginRight: 8 }}>
                        {item.poiName}
                    </Text>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                        ID: {item.poiId}
                    </Text>
                </div>

                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                    {getStatusTag(item.status)}
                    <Text type="secondary" style={{ fontSize: 12, marginLeft: 8 }}>
                        {dayjs(item.createTime).format('MM-DD HH:mm')}
                    </Text>
                </div>

                {item.content && (
                    <Text type="secondary" style={{ fontSize: 12 }} ellipsis>
                        {item.content}
                    </Text>
                )}
            </div>

            <Space>
                <Button
                    type="primary"
                    size="small"
                    icon={<EyeOutlined />}
                    onClick={() => onViewResult(item)}
                    disabled={!canViewResult}
                >
                    查看结果
                </Button>
            </Space>
        </div>
    );
};

export default TaskListItem;
