import React from 'react';
import { Button, Badge, Tooltip } from 'antd';
import { UnorderedListOutlined } from '@ant-design/icons';

interface TaskListButtonProps {
    runningCount: number;
    onClick: () => void;
    loading?: boolean;
}

const TaskListButton: React.FC<TaskListButtonProps> = ({ runningCount, onClick, loading = false }) => {
    return (
        <Tooltip title="任务列表">
            <Badge count={runningCount} size="small" offset={[-3, 3]}>
                <Button
                    type="text"
                    icon={<UnorderedListOutlined />}
                    onClick={onClick}
                    loading={loading}
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: 40,
                        height: 40,
                        borderRadius: 8,
                        border: 'none',
                        background: 'rgba(255, 255, 255, 0.1)',
                        color: '#ffffff',
                        fontSize: 18,
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                    }}
                />
            </Badge>
        </Tooltip>
    );
};

export default TaskListButton;
