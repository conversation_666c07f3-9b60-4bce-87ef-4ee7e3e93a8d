{"name": "waimai_cd_fe_crm_bdservice", "private": true, "version": "0.0.0", "scripts": {"prepare": "husky install", "dev": "VITE_API_PREFIX=/xianfu/api/bdservice DEPLOY_ENV=dev vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint --ext .ts,.tsx,.js,.jsx ./src", "sync-dts": "node node_modules/.bin/sync-dts", "parse-yapi": "node node_modules/.bin/parse-yapi", "test": "vitest"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["bash -c tsc", "prettier --parser=typescript --write"]}, "dependencies": {"@ai/mss-upload-js": "^1.1.6-beta13", "@ant-design/icons": "^5.2.6", "@ant-design/plots": "^1.2.5", "@antv/g2plot": "^2.4.31", "@mfe/bellwether-route": "^1.0.9", "@mfe/cc-api-caller-pc": "^0.2.8", "@mfe/cc-ocrm-utils": "^0.0.6", "@mtfe/sso-web": "^2.4.1", "@roo/roo": "^1.15.1-beta.2", "@roo/roo-plus": "^0.4.1-beta.2", "ahooks": "^3.7.11", "antd": "^5.15.4", "antd-table-saveas-excel": "^2.1.4", "axios": "^1.6.8", "classnames": "^2.5.1", "dayjs": "^1.11.11", "immer": "^10.0.2", "lodash": "^4.17.21", "marked": "^9.1.6", "quill": "^2.0.2", "rc-virtual-list": "^3.16.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^10.1.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "swr": "^2.3.3", "zustand": "^5.0.1"}, "devDependencies": {"@testing-library/react": "^15.0.2", "@types/lodash": "^4.17.0", "@types/node": "^20.2.0", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@typescript-eslint/eslint-plugin": "^5.55.0", "@typescript-eslint/parser": "^5.55.0", "@vitejs/plugin-react-swc": "^3.2.0", "@vitest/coverage-v8": "1", "eslint": "^8.36.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.37.1", "eslint-plugin-unused-imports": "^4.1.3", "husky": "^7.0.0", "jsdom": "^24.0.1", "lint-staged": "^13.2.2", "prettier": "^2.7.1", "sass": "^1.63.6", "typescript": "^4.9.3", "vite": "^5.1.8", "vite-plugin-html-template": "^1.2.0", "vite-plugin-mpa": "^1.2.0", "vite-plugin-qiankun": "^1.0.15", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.0.5", "vitest": "^1"}, "resolutions": {"cheerio": "1.0.0-rc.12", "rollup": "4.30.0", "cssstyle": "4.0.1"}, "volta": {"node": "18.20.5", "yarn": "1.22.22"}}