import { View, Text, Image, TouchableOpacity } from '@mrn/react-native';
import React from 'react';

import TWS from '../../../../TWS';
import Condition from '../../../Condition/Condition';
import { PoiItemProps } from '../types/PoiSelector';

/**
 * POI 项目组件
 */
export const PoiItem = ({
    onPress,
    isLast,
    id,
    name,
    url,
    online,
    labels,
    tags,
    isMultiSelect,
    isSelected,
    onToggleSelect,
    tagNameList,
}: PoiItemProps) => {
    const TheView: any = onPress ? TouchableOpacity : View;

    return (
        <TheView
            onPress={isMultiSelect ? onToggleSelect : onPress}
            style={[
                TWS.row(),
                {
                    borderWidth: 0.5,
                    borderRadius: 10,
                    borderColor: isSelected ? '#FFD100' : '#E7E8E9',
                    backgroundColor: isSelected ? '#FFFBF0' : '#fff',
                    padding: 12,
                    marginBottom: !isLast ? 10 : 0,
                },
            ]}
        >
            {isMultiSelect && (
                <View
                    style={{
                        marginRight: 8,
                        justifyContent: 'center',
                    }}
                >
                    <View
                        style={[
                            TWS.square(20),
                            {
                                borderRadius: 4,
                                borderWidth: 2,
                                borderColor: isSelected ? '#FFD100' : '#E7E8E9',
                                backgroundColor: isSelected
                                    ? '#FFD100'
                                    : '#fff',
                                justifyContent: 'center',
                                alignItems: 'center',
                            },
                        ]}
                    >
                        {isSelected && (
                            <Text
                                style={{
                                    color: '#fff',
                                    fontSize: 12,
                                    fontWeight: 'bold',
                                }}
                            >
                                ✓
                            </Text>
                        )}
                    </View>
                </View>
            )}
            <View
                style={{
                    borderRadius: 8,
                    marginRight: 8,
                    overflow: 'hidden',
                }}
            >
                <Image
                    source={{ uri: url }}
                    style={[TWS.square(36), { borderRadius: 8 }]}
                />
            </View>

            <View style={{ flex: 1 }}>
                <View
                    style={[
                        TWS.row(),
                        { flexWrap: 'wrap', alignItems: 'center' },
                    ]}
                >
                    {tagNameList?.map((v) => (
                        <View
                            style={{
                                backgroundColor: '#FFF5F6',
                                paddingHorizontal: 4,
                                maxWidth: 100,
                                justifyContent: 'center',
                                marginRight: 4,
                                borderRadius: 2,
                                height: 16,
                            }}
                        >
                            <Text
                                style={{
                                    color: '#FF192D',
                                    fontSize: 11,
                                }}
                                numberOfLines={1}
                            >
                                {v}
                            </Text>
                        </View>
                    ))}
                    <Text
                        numberOfLines={1}
                        ellipsizeMode={'middle'}
                        style={{
                            color: '#222',
                            fontSize: 16,
                            flex: 1,
                        }}
                    >
                        {name}
                    </Text>
                    <Condition condition={[online !== undefined]}>
                        <View
                            style={[
                                TWS.row(),
                                TWS.center(),
                                {
                                    borderRadius: 8,
                                    backgroundColor: online
                                        ? '#E6FAF2'
                                        : '#F1F1F1',
                                    paddingHorizontal: 4,
                                    paddingVertical: 2,
                                },
                            ]}
                        >
                            <View
                                style={[
                                    TWS.circle(8),
                                    {
                                        backgroundColor: online
                                            ? '#58C080'
                                            : '#858688',
                                        marginRight: 4,
                                    },
                                ]}
                            />
                            <Text style={{ fontSize: 10 }}>
                                {online ? '在线' : '下线'}
                            </Text>
                        </View>
                    </Condition>
                </View>

                <Condition condition={[Boolean(id)]}>
                    <Text
                        style={{
                            color: '#666',
                            fontSize: 14,
                        }}
                    >
                        ID:{id}
                    </Text>
                </Condition>
                <View
                    style={{
                        flexWrap: 'wrap',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginTop: 2,
                    }}
                >
                    {labels?.map((v) => (
                        <Text style={{ color: '#666' }} key={String(v.value)}>
                            {v.label}:{v.value}
                        </Text>
                    ))}
                    {tags?.map((v) => (
                        <View
                            style={{
                                borderColor: '#666',
                                borderWidth: 0.5,
                                borderRadius: 4,
                                paddingHorizontal: 4,
                                paddingVertical: 2,
                            }}
                            key={v}
                        >
                            <Text style={{ fontSize: 10 }}>{v}</Text>
                        </View>
                    ))}
                </View>
            </View>
        </TheView>
    );
};
