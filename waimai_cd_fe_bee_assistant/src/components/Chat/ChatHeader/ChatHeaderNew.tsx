import { StyleSheet, TouchableOpacity, View, Image } from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import { useDebounceFn } from 'ahooks';
import React, { useContext } from 'react';

import RootTagContext from '../../../hooks/rootTagContext';
import { useSendMessage } from '../../../hooks/useSendMessage';
import { EntryPointType } from '../../../types';
import { TaskListManager } from '../../TaskList';
import { useRefreshMessage } from '../hooks';

import NetImages from '@/assets/images/homeRefactor';
import newSessionImg from '@/assets/images/voice/new_session.png';
import Condition from '@/components/Condition/Condition';
import RNImage from '@/components/RNImage';
import useMessage from '@/hooks/useMessage';
import { useUiState } from '@/store/uiState';
import TWS from '@/TWS';
import { trackButtonClick } from '@/utils/track';

interface ChatHeader {
    navigator: { pop: () => void };
}

const styles = StyleSheet.create({
    container: {
        position: 'relative',
        marginTop: 14.5,
        paddingHorizontal: 16,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    closeCircle: {
        height: 24,
        width: 24,
        borderRadius: 12,
        backgroundColor: 'rgba(255, 255, 255, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    complex: {
        paddingHorizontal: 16,
        zIndex: 10,
        flexDirection: 'row',
    },
    simple: {
        paddingHorizontal: 16,
        flexDirection: 'row',
        justifyContent: 'flex-end',
        paddingBottom: 12,
        left: 0,
        right: 0,
    },
    hi: {
        fontWeight: '900',
        fontSize: 16,
        lineHeight: 18,
    },
    title: {
        fontWeight: '600',
        fontSize: 14,
        lineHeight: 18,
    },
    text: {
        fontSize: 11,
        lineHeight: 15,
        marginTop: 2,
    },
    underline: {
        textDecorationLine: 'underline',
    },
    noticeText: {
        color: '#666',
        textAlign: 'center',
    },
    link: {
        color: '#FF6A00',
    },
    headMid: {
        justifyContent: 'center',
        marginLeft: 8,
        flex: 1,
    },
    image: {
        height: 24,
        width: 24,
    },
});

const ChatHeader = () => {
    const { pop } = useContext(RootTagContext);

    const { run: onClose } = useDebounceFn(
        () => {
            pop();
        },
        { wait: 200 },
    );

    const showHome = useUiState((state) => state.showHome);
    const setShowHome = useUiState((state) => state.setShowHome);
    const refreshMessage = useRefreshMessage();
    const getHistoryMessageList = useMessage(
        (state) => state.getHistoryMessageList,
    );

    const { send } = useSendMessage();

    const handleSendMessage = (content: string) => {
        send(content, EntryPointType.USER);
    };

    return (
        <View style={[styles.container]}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <TouchableOpacity onPress={onClose}>
                    <Icon type="left" size={20} />
                </TouchableOpacity>
                <Condition condition={[!showHome]}>
                    <Image
                        source={{
                            uri: NetImages.miniIcon,
                        }}
                        style={[TWS.square(40)]}
                    />
                </Condition>
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Condition condition={[!showHome, showHome]}>
                    <TouchableOpacity onPress={() => refreshMessage()}>
                        <RNImage source={newSessionImg} style={{ width: 24 }} />
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => {
                            setShowHome(false);
                            getHistoryMessageList();
                            trackButtonClick('history');
                        }}
                    >
                        <RNImage
                            source={NetImages.historyIcon}
                            style={{ width: 22 }}
                        />
                    </TouchableOpacity>
                </Condition>
                <TaskListManager onSendMessage={handleSendMessage} />
            </View>
        </View>
    );
};

export default ChatHeader;
