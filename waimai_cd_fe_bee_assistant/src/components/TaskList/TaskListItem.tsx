import {
    StyleSheet,
    TouchableOpacity,
    View,
    Text,
    Image,
} from '@mrn/react-native';
import dayjs from 'dayjs';
import React from 'react';

import { TaskItem } from '../../api/taskApi';

// 扩展TaskItem类型，包含createTime
interface TaskItemWithTime extends TaskItem {
    createTime: string;
}

interface TaskListItemProps {
    /** 任务项数据 */
    item: TaskItemWithTime;
    /** 查看结果回调 */
    onViewResult: (item: TaskItemWithTime) => void;
}

const TaskListItem: React.FC<TaskListItemProps> = ({ item, onViewResult }) => {
    const getStatusConfig = (status: string) => {
        switch (status) {
            case 'success':
                return {
                    text: '成功',
                    color: '#52C41A',
                    backgroundColor: '#F6FFED',
                };
            case 'fail':
                return {
                    text: '失败',
                    color: '#FF4D4F',
                    backgroundColor: '#FFF2F0',
                };
            case 'init':
                return {
                    text: '进行中',
                    color: '#1890FF',
                    backgroundColor: '#F0F9FF',
                };
            default:
                return {
                    text: status,
                    color: '#666666',
                    backgroundColor: '#F5F5F5',
                };
        }
    };

    const statusConfig = getStatusConfig(item.status);
    const canViewResult = item.status === 'success' || item.status === 'fail';

    return (
        <View style={styles.container}>
            <View style={styles.avatarContainer}>
                {item.poiAvator ? (
                    <Image
                        source={{ uri: item.poiAvator }}
                        style={styles.avatar}
                    />
                ) : (
                    <View style={[styles.avatar, styles.defaultAvatar]}>
                        <Text style={styles.avatarText}>店</Text>
                    </View>
                )}
            </View>

            <View style={styles.content}>
                <View style={styles.header}>
                    <Text style={styles.poiName} numberOfLines={1}>
                        {item.poiName}
                    </Text>
                    <Text style={styles.poiId}>ID: {item.poiId}</Text>
                </View>

                <View style={styles.statusRow}>
                    <View
                        style={[
                            styles.statusTag,
                            { backgroundColor: statusConfig.backgroundColor },
                        ]}
                    >
                        <Text
                            style={[
                                styles.statusText,
                                { color: statusConfig.color },
                            ]}
                        >
                            {statusConfig.text}
                        </Text>
                    </View>
                    <Text style={styles.createTime}>
                        {item.createTime
                            ? dayjs(item.createTime).format('MM-DD HH:mm')
                            : ''}
                    </Text>
                </View>

                {item.content && (
                    <Text style={styles.contentText} numberOfLines={2}>
                        {item.content}
                    </Text>
                )}
            </View>

            <TouchableOpacity
                style={[
                    styles.actionButton,
                    !canViewResult && styles.actionButtonDisabled,
                ]}
                onPress={() => onViewResult(item)}
                disabled={!canViewResult}
                activeOpacity={0.7}
            >
                <Text
                    style={[
                        styles.actionButtonText,
                        !canViewResult && styles.actionButtonTextDisabled,
                    ]}
                >
                    查看结果
                </Text>
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 16,
        paddingHorizontal: 16,
        backgroundColor: '#ffffff',
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    avatarContainer: {
        marginRight: 12,
    },
    avatar: {
        width: 40,
        height: 40,
        borderRadius: 20,
    },
    defaultAvatar: {
        backgroundColor: '#f5f5f5',
        alignItems: 'center',
        justifyContent: 'center',
    },
    avatarText: {
        fontSize: 12,
        color: '#999999',
        fontWeight: '500',
    },
    content: {
        flex: 1,
        marginRight: 12,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 4,
    },
    poiName: {
        fontSize: 14,
        fontWeight: '600',
        color: '#222222',
        marginRight: 8,
        flex: 1,
    },
    poiId: {
        fontSize: 12,
        color: '#999999',
    },
    statusRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 4,
    },
    statusTag: {
        paddingHorizontal: 8,
        paddingVertical: 2,
        borderRadius: 4,
        marginRight: 8,
    },
    statusText: {
        fontSize: 12,
        fontWeight: '500',
    },
    createTime: {
        fontSize: 12,
        color: '#999999',
    },
    contentText: {
        fontSize: 12,
        color: '#666666',
        lineHeight: 16,
    },
    actionButton: {
        backgroundColor: '#FFD100',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 4,
    },
    actionButtonDisabled: {
        backgroundColor: '#f5f5f5',
    },
    actionButtonText: {
        fontSize: 12,
        fontWeight: '500',
        color: '#222222',
    },
    actionButtonTextDisabled: {
        color: '#cccccc',
    },
});

export default TaskListItem;
