import { StyleSheet, TouchableOpacity, View, Text } from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React from 'react';

interface TaskListButtonProps {
    /** 运行中任务数量 */
    runningCount: number;
    /** 点击事件 */
    onPress: () => void;
    /** 是否加载中 */
    loading?: boolean;
}

const TaskListButton: React.FC<TaskListButtonProps> = ({
    runningCount,
    onPress,
    loading = false,
}) => {
    return (
        <TouchableOpacity
            style={styles.container}
            onPress={onPress}
            disabled={loading}
            activeOpacity={0.7}
        >
            <View style={styles.iconContainer}>
                <Icon size={20} type={'add'} />
                {runningCount > 0 && (
                    <View style={styles.badge}>
                        <Text style={styles.badgeText}>
                            {runningCount > 99 ? '99+' : runningCount}
                        </Text>
                    </View>
                )}
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    container: {
        width: 40,
        height: 40,
        borderRadius: 8,
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: 8,
    },
    iconContainer: {
        position: 'relative',
        alignItems: 'center',
        justifyContent: 'center',
    },
    badge: {
        position: 'absolute',
        top: -8,
        right: -8,
        backgroundColor: '#FF4D4F',
        borderRadius: 10,
        minWidth: 20,
        height: 20,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 4,
        borderWidth: 2,
        borderColor: '#ffffff',
    },
    badgeText: {
        color: '#ffffff',
        fontSize: 12,
        fontWeight: 'bold',
        textAlign: 'center',
        lineHeight: 16,
    },
});

export default TaskListButton;
