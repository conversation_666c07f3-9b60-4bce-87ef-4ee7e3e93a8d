# 任务列表功能

## 功能概述

任务列表功能为移动端聊天界面提供了任务状态查看和管理能力，用户可以通过头部的任务按钮查看当前运行中的任务数量，并点击进入任务列表抽屉查看详细信息。

## 组件结构

```text
TaskList/
├── TaskListManager.tsx      # 主管理组件，负责状态管理和轮询
├── TaskListButton.tsx       # 任务列表入口按钮
├── TaskListDrawer.tsx       # 任务列表抽屉面板
├── TaskListItem.tsx         # 单个任务项组件
├── index.ts                 # 导出文件
└── README.md               # 说明文档
```

## 使用方式

### 1. 在聊天头部集成

```tsx
import { TaskListManager } from '@/components/TaskList';
import { useSendMessage } from '@/hooks/useSendMessage';
import { EntryPointType } from '@/types';

const ChatHeader = () => {
    const { send } = useSendMessage();
    
    const handleSendMessage = (content: string) => {
        send(content, EntryPointType.USER);
    };

    return (
        <View style={styles.header}>
            {/* 其他头部内容 */}
            <TaskListManager onSendMessage={handleSendMessage} />
        </View>
    );
};
```

### 2. 独立使用组件

```tsx
import { TaskListButton, TaskListDrawer } from '@/components/TaskList';

const MyComponent = () => {
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [runningCount, setRunningCount] = useState(0);

    return (
        <>
            <TaskListButton
                runningCount={runningCount}
                onPress={() => setDrawerVisible(true)}
            />
            <TaskListDrawer
                visible={drawerVisible}
                onClose={() => setDrawerVisible(false)}
                onSendMessage={(content) => console.log(content)}
            />
        </>
    );
};
```

## API 接口

### 获取任务状态

- **接口**: `/bee/v2/bdaiassistant/common/task/list`
- **方法**: GET
- **响应**: `{ runnings: number }`

### 获取任务列表

- **接口**: `/bee/v2/bdaiassistant/job/list`
- **方法**: GET
- **响应**: 包含任务组列表和统计信息

## 功能特性

1. **实时状态更新**: 2秒轮询获取任务状态
2. **徽章显示**: 显示运行中任务数量
3. **分类查看**: 支持全部、进行中、成功、失败任务分类
4. **一键查询**: 点击任务项自动发送查询消息
5. **响应式设计**: 适配移动端界面

## 注意事项

1. 组件会在挂载时自动开始轮询，卸载时自动停止
2. 任务查询消息会通过 `onSendMessage` 回调发送到聊天机器人
3. 只有成功或失败状态的任务才能点击查看结果
4. 抽屉面板支持手势关闭和点击遮罩关闭
