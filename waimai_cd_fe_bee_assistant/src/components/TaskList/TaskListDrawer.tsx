import {
    StyleSheet,
    View,
    Text,
    TouchableOpacity,
    FlatList,
    Dimensions,
    Modal,
    Animated,
} from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import { Toast } from '@roo/roo-rn';
import React, { useState, useEffect, useMemo, useRef } from 'react';

import TaskListItem from './TaskListItem';
import { TaskItem, TaskListResponse, getTaskList } from '../../api/taskApi';

// 扩展TaskItem类型，包含createTime
interface TaskItemWithTime extends TaskItem {
    createTime: string;
}

interface TaskListDrawerProps {
    /** 是否显示抽屉 */
    visible: boolean;
    /** 关闭抽屉回调 */
    onClose: () => void;
    /** 发送消息回调 */
    onSendMessage: (content: string) => void;
}

type TabType = 'all' | 'running' | 'success' | 'fail';

const { height: screenHeight, width: screenWidth } = Dimensions.get('window');

const TaskListDrawer: React.FC<TaskListDrawerProps> = ({
    visible,
    onClose,
    onSendMessage,
}) => {
    const [activeTab, setActiveTab] = useState<TabType>('all');
    const [taskData, setTaskData] = useState<TaskListResponse | null>(null);
    const [loading, setLoading] = useState(false);

    // 动画值
    const translateX = useRef(new Animated.Value(screenWidth)).current;
    const opacity = useRef(new Animated.Value(0)).current;

    // 获取任务列表数据
    const fetchTaskList = async () => {
        setLoading(true);
        try {
            const data = await getTaskList();
            setTaskData(data);
        } catch (error) {
            console.error('获取任务列表失败:', error);
            Toast.open('获取任务列表失败');
        } finally {
            setLoading(false);
        }
    };

    // 动画控制
    useEffect(() => {
        if (visible) {
            // 打开抽屉动画
            Animated.parallel([
                Animated.timing(translateX, {
                    toValue: 0,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.timing(opacity, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true,
                }),
            ]).start();
        } else {
            // 关闭抽屉动画
            Animated.parallel([
                Animated.timing(translateX, {
                    toValue: screenWidth,
                    duration: 250,
                    useNativeDriver: true,
                }),
                Animated.timing(opacity, {
                    toValue: 0,
                    duration: 250,
                    useNativeDriver: true,
                }),
            ]).start();
        }
    }, [visible, translateX, opacity]);

    // 当抽屉打开时获取数据
    useEffect(() => {
        if (visible) {
            fetchTaskList();
        }
    }, [visible]);

    // 过滤任务数据
    const filteredTasks = useMemo(() => {
        if (!taskData?.jobList) {
            return [];
        }

        const allTasks: TaskItemWithTime[] = [];
        taskData.jobList.forEach((group) => {
            group.itemList.forEach((item) => {
                allTasks.push({ ...item, createTime: group.createTime });
            });
        });

        switch (activeTab) {
            case 'running':
                return allTasks.filter((task) => task.status === 'init');
            case 'success':
                return allTasks.filter((task) => task.status === 'success');
            case 'fail':
                return allTasks.filter((task) => task.status === 'fail');
            default:
                return allTasks;
        }
    }, [taskData, activeTab]);

    // 处理查看结果
    const handleViewResult = (item: TaskItemWithTime) => {
        if (item.status !== 'success' && item.status !== 'fail') {
            return;
        }

        // 构造查询消息
        const message = `查看商家 ${item.poiName}(ID:${item.poiId}) 的任务结果`;
        onSendMessage(message);
        onClose();
    };

    // 处理关闭抽屉
    const handleClose = () => {
        Animated.parallel([
            Animated.timing(translateX, {
                toValue: screenWidth,
                duration: 250,
                useNativeDriver: true,
            }),
            Animated.timing(opacity, {
                toValue: 0,
                duration: 250,
                useNativeDriver: true,
            }),
        ]).start(() => {
            onClose();
        });
    };

    // 标签页配置
    const tabs = [
        { key: 'all', label: '全部', count: taskData?.total || 0 },
        {
            key: 'running',
            label: '进行中',
            count: filteredTasks.filter((t) => t.status === 'init').length,
        },
        { key: 'success', label: '成功', count: taskData?.success || 0 },
        { key: 'fail', label: '失败', count: taskData?.fail || 0 },
    ];

    const renderTabItem = (tab: any) => {
        const isActive = activeTab === tab.key;
        return (
            <TouchableOpacity
                key={tab.key}
                style={[styles.tabItem, isActive && styles.tabItemActive]}
                onPress={() => setActiveTab(tab.key as TabType)}
                activeOpacity={0.7}
            >
                <Text
                    style={[styles.tabText, isActive && styles.tabTextActive]}
                >
                    {tab.label}
                </Text>
                {tab.count > 0 && (
                    <Text
                        style={[
                            styles.tabCount,
                            isActive && styles.tabCountActive,
                        ]}
                    >
                        ({tab.count})
                    </Text>
                )}
            </TouchableOpacity>
        );
    };

    const renderTaskItem = ({ item }: { item: TaskItemWithTime }) => (
        <TaskListItem item={item} onViewResult={handleViewResult} />
    );

    const renderEmptyState = () => (
        <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>暂无任务数据</Text>
        </View>
    );

    return (
        <Modal
            visible={visible}
            animationType="none"
            transparent={true}
            onRequestClose={handleClose}
        >
            <View style={styles.overlay}>
                <Animated.View
                    style={[
                        styles.overlayBackground,
                        {
                            opacity: opacity,
                        },
                    ]}
                >
                    <TouchableOpacity
                        style={styles.overlayTouchable}
                        activeOpacity={1}
                        onPress={handleClose}
                    />
                </Animated.View>
                <Animated.View
                    style={[
                        styles.drawer,
                        {
                            transform: [{ translateX }],
                        },
                    ]}
                >
                    {/* 头部 */}
                    <View style={styles.header}>
                        <Text style={styles.title}>今日任务</Text>
                        <TouchableOpacity
                            onPress={handleClose}
                            style={styles.closeButton}
                        >
                            <Icon type="close" size={20} />
                        </TouchableOpacity>
                    </View>

                    {/* 标签页 */}
                    <View style={styles.tabContainer}>
                        {tabs.map(renderTabItem)}
                    </View>

                    {/* 任务列表 */}
                    <View style={styles.listContainer}>
                        {loading ? (
                            <View style={styles.loadingContainer}>
                                <Text style={styles.loadingText}>
                                    加载中...
                                </Text>
                            </View>
                        ) : (
                            <FlatList
                                data={filteredTasks}
                                renderItem={renderTaskItem}
                                keyExtractor={(item, index) =>
                                    `${item.poiId}_${index}`
                                }
                                showsVerticalScrollIndicator={false}
                                ListEmptyComponent={renderEmptyState}
                            />
                        )}
                    </View>
                </Animated.View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        flexDirection: 'row',
    },
    overlayBackground: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    overlayTouchable: {
        flex: 1,
    },
    drawer: {
        backgroundColor: '#ffffff',
        width: screenWidth * 0.8,
        maxWidth: 320,
        minWidth: 280,
        height: screenHeight,
        shadowColor: '#000',
        shadowOffset: {
            width: -2,
            height: 0,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 16,
        paddingTop: 60, // 增加顶部安全区域
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    title: {
        fontSize: 18,
        fontWeight: '600',
        color: '#222222',
    },
    closeButton: {
        padding: 4,
    },
    tabContainer: {
        flexDirection: 'row',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
        flexWrap: 'wrap',
    },
    tabItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        marginRight: 8,
        marginBottom: 8,
        borderRadius: 16,
        backgroundColor: '#f5f5f5',
    },
    tabItemActive: {
        backgroundColor: '#FFD100',
    },
    tabText: {
        fontSize: 14,
        color: '#666666',
        fontWeight: '500',
    },
    tabTextActive: {
        color: '#222222',
    },
    tabCount: {
        fontSize: 12,
        color: '#999999',
        marginLeft: 4,
    },
    tabCountActive: {
        color: '#666666',
    },
    listContainer: {
        flex: 1,
    },
    loadingContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    loadingText: {
        fontSize: 14,
        color: '#999999',
    },
    emptyContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 14,
        color: '#999999',
        marginTop: 12,
    },
});

export default TaskListDrawer;
