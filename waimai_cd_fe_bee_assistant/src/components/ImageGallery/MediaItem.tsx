import Video from '@mfe/react-native-video';
import KNB from '@mrn/mrn-knb';
import {
    View,
    Image,
    PixelRatio,
    Platform,
    StyleSheet,
    TouchableOpacity,
} from '@mrn/react-native';
import React, { useContext } from 'react';
import WebView from 'react-native-webview';

import playerImg from '../../assets/images/player.png';
import RootTagContext from '../../hooks/rootTagContext';
import EnhancedProgressiveImage from '../EnhancedProgressiveImage';

const devicePixelRatio = PixelRatio.get();

const styles = StyleSheet.create({
    child: {
        backgroundColor: '#eee',
        marginRight: 8,
        marginBottom: 8,
        borderRadius: 4,
        width: 71.5,
        height: 90,
        overflow: 'hidden',
    },
});
const MediaItem = ({ media, medias }) => {
    const curImage = media.image || media.video;
    const images = medias.filter((v) => v.image).map((v) => v.image);
    const { backing } = useContext(RootTagContext);

    // 增加判空逻辑
    if (!curImage) {
        return null;
    }
    // 视频
    if (curImage.endsWith('.mp4') || media.video) {
        return (
            <TouchableOpacity
                style={[
                    styles.child,
                    {
                        position: 'relative',
                    },
                ]}
                key={curImage}
                onPress={() => KNB.openPage({ url: curImage })}
            >
                {Platform.OS === 'ios' ? (
                    <>
                        <Image
                            source={playerImg}
                            style={{
                                left: 20,
                                top: 30,
                                width: 30,
                                height: 30,
                                zIndex: 1000,
                                position: 'absolute',
                            }}
                        />
                        <Video
                            source={{ uri: curImage }}
                            rate={1.0}
                            volume={1.0}
                            paused={true}
                            // resizeMode="cover"
                            style={{ width: 71.5, height: 90 }}
                            onLoad={(metaData) => {
                                console.log(metaData);
                            }} // 视频加载时的回调函数
                        />
                    </>
                ) : // 退出页面时提前销毁webview
                !backing ? (
                    <WebView
                        allowsInlineMediaPlayback={true}
                        scalesPageToFit={true}
                        containerStyle={[
                            {
                                flex: 1,
                                width: '100%',
                            },
                        ]}
                        pointerEvents={'none'}
                        scrollEnabled={false}
                        showsHorizontalScrollIndicator={false}
                        showsVerticalScrollIndicator={false}
                        source={{
                            html: `<video src=${curImage} preload='auto' playsInline style='width: ${
                                71.5 * 1.4 * devicePixelRatio
                            }px; height: ${
                                90 * 1.4 * devicePixelRatio
                            }px' controls/>`,
                        }}
                        mixedContentMode="compatibility"
                        mediaPlaybackRequiresUserAction={
                            Platform.OS !== 'android' ||
                            Number(Platform.Version) >= 17
                                ? false
                                : undefined
                        }
                        userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36"
                    />
                ) : null}
            </TouchableOpacity>
        );
    }

    return (
        <View style={styles.child} key={curImage}>
            <EnhancedProgressiveImage
                style={{
                    width: '100%',
                    height: '100%',
                }}
                resizeMode="cover"
                src={curImage}
                imageUrls={images || []}
            />
        </View>
    );
};
export default MediaItem;
