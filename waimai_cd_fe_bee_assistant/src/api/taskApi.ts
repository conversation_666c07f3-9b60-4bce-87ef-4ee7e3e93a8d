import { apiCaller } from '@mfe/cc-api-caller-bee';

/**
 * 任务状态接口响应
 */
export interface TaskStatusResponse {
    /** 正在运行中的任务个数 */
    runnings: number;
}

/**
 * 任务项接口
 */
export interface TaskItem {
    /** 任务类型 */
    type: string;
    /** 商家ID */
    poiId: number;
    /** 商家名称 */
    poiName: string;
    /** 商家头像 */
    poiAvator: string;
    /** 任务状态 success: 成功；fail : 失败；init: 运行中；*/
    status: string;
    /** 能力类型 */
    abilityType: number;
    /** 操作类型 */
    operationType: number;
    /** 内容 */
    content: string;
}

/**
 * 任务组接口
 */
export interface TaskGroup {
    /** 创建时间 */
    createTime: string;
    itemList: TaskItem[];
}

/**
 * 任务列表接口响应
 */
export interface TaskListResponse {
    jobList: TaskGroup[];
    /** 总数 */
    total: number;
    /** 成功个数 */
    success: number;
    /** 失败个数 */
    fail: number;
}

/**
 * API响应基础结构
 */
interface ApiResponse<T> {
    code: number;
    msg: string;
    data: T;
}

/**
 * 获取任务状态
 * @returns 任务状态数据
 */
export const getTaskStatus = async (): Promise<TaskStatusResponse | null> => {
    try {
        const res: ApiResponse<TaskStatusResponse> = await apiCaller.get(
            '/bee/v2/bdaiassistant/common/task/list',
            {},
        );

        if (res.code === 0) {
            return res.data;
        }

        return null;
    } catch (error) {
        console.error('获取任务状态失败:', error);
        return null;
    }
};

/**
 * 获取任务列表
 * @returns 任务列表数据
 */
export const getTaskList = async (): Promise<TaskListResponse | null> => {
    try {
        const res: ApiResponse<any> = await apiCaller.get(
            '/bee/v2/bdaiassistant/job/list',
            {},
        );

        if (res.code === 0) {
            // 根据API定义，响应结构需要调整
            return {
                jobList: res.data.jobList || [],
                total: res.data.total || 0,
                success: res.data.success || 0,
                fail: res.data.fail || 0,
            };
        }

        return null;
    } catch (error) {
        console.error('获取任务列表失败:', error);
        return null;
    }
};
